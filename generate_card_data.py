import mysql.connector
import mysql.connector.pooling
from mysql.connector import Error
import random
import string
from datetime import datetime, timedelta
import threading
import time
from concurrent.futures import ThreadPoolExecutor
import queue
import logging
import uuid
import hashlib

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(threadName)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class CardDataGenerator:
    def __init__(self):
        # 数据库连接配置
        self.db_config = {
            'host': 'nexus-master.cl4kksw821u9.ap-east-1.rds.amazonaws.com',
            'port': 3306,
            'database': 'staging_card',
            'user': 'root',
            'password': 'BwYvW64msQ5Lndu2Umpn',
            'autocommit': False,  # 使用手动事务
            'charset': 'utf8mb4',
            'use_unicode': True,
            'connect_timeout': 60,
            'sql_mode': 'STRICT_TRANS_TABLES,NO_ZERO_DATE,NO_ZERO_IN_DATE,ERROR_FOR_DIVISION_BY_ZERO'
        }
        
        # 初始化连接池（延迟到设置线程数后）
        self.connection_pool = None
        
        # 配置参数
        self.target_year = 2025
        self.total_records = 1000  # 总生成条数
        self.thread_count = 5  # 线程数
        
        # 基础数据配置
        self.card_types = [1, 31, 32, 33, 34, 35, 36, 37, 38, 42, 45, 47, 52, 60, 70, 71]
        self.face_values = [20, 30, 50, 100, 150, 200]
        self.ck_company_ids = [2, 3, 8, 9, 11, 12, 13, 14, 23, 24, 25, 28, 32, 38, 40, 44, 46, 49, 51, 54, 56, 57, 61, 64, 65]
        self.sk_company_ids = [4, 10, 15, 16, 17, 18, 19, 20, 21, 22, 26, 27, 30, 31, 33, 34, 36, 37, 39, 41, 42, 45, 47, 48, 50, 52, 53, 55, 58, 62]
        
        # 出卡商和操作人映射关系
        self.ck_company_owners = {
            2: [4, 12, 13, 14, 15, 21, 49, 140, 143, 144, 145, 160],
            3: [5, 17, 53, 69],
            8: [26, 54, 56, 57, 58, 59, 60, 61],
            11: [27, 50, 64, 70, 80, 81, 123, 124, 125],
            12: [19, 42, 91],
            13: [20],
            14: [22],
            23: [44, 74],
            24: [46, 47],
            25: [65, 67, 68, 75, 76, 77, 78, 92, 152, 153, 167, 235, 236, 243, 249, 255, 262],
            28: [83, 84, 85],
            32: [95, 100, 103, 136],
            38: [121, 193, 194, 197, 198, 207, 208, 209, 233, 234, 239, 245, 246, 247, 248, 256, 257],
            40: [126, 127, 128, 129],
            44: [163, 165, 170, 176, 177, 178, 211, 215, 216, 217, 222, 230, 231, 244, 280, 286, 287, 288, 289, 308],
            46: [174],
            49: [184],
            51: [185],
            54: [203],
            56: [205, 206, 218, 219, 220, 221],
            57: [241, 242],
            61: [254],
            64: [265, 266, 267, 275, 276, 277, 278, 285, 292, 294, 295, 296, 297, 298, 299, 300, 301, 302, 303, 304, 305, 306, 307],
            65: [283]
        }
        
        # 收卡商和操作人映射关系
        self.sk_company_owners = {
            4: [6, 34, 35, 36],
            10: [16],
            15: [23, 33],
            16: [24, 25, 37, 51, 71],
            17: [28, 29, 30, 32, 79],
            19: [38, 52, 137],
            20: [39, 40, 55, 147, 238],
            21: [41],
            22: [43],
            26: [66, 72, 110, 141, 142, 187],
            27: [73, 86, 111, 149],
            30: [88],
            31: [89],
            33: [97, 99, 101],
            34: [104, 105, 106, 107, 108, 119, 146, 188, 189, 190, 191],
            36: [115, 116, 117, 118],
            37: [120, 134, 138, 227, 229],
            39: [130, 131, 132, 133],
            41: [135],
            45: [164, 168, 169, 179, 180, 181, 213, 214, 232],
            47: [175],
            48: [183],
            50: [186],
            52: [199],
            53: [200, 201, 202],
            55: [204],
            58: [250, 252],
            62: [258, 259, 260, 261, 271, 274]
        }

    def init_connection_pool(self):
        """初始化数据库连接池"""
        try:
            # 动态设置连接池大小，确保能支持设置的线程数
            pool_size = max(self.thread_count + 2, 10)  # 至少10个连接，或线程数+2
            self.connection_pool = mysql.connector.pooling.MySQLConnectionPool(
                pool_name="card_data_pool",
                pool_size=pool_size,  # 动态连接池大小
                pool_reset_session=True,
                **self.db_config
            )
            logger.info(f"数据库连接池大小: {pool_size}")
            logger.info("数据库连接池初始化成功")
        except Error as e:
            logger.error(f"数据库连接池初始化失败: {e}")
            raise e

    def get_connection(self):
        """从连接池获取数据库连接"""
        try:
            connection = self.connection_pool.get_connection()
            return connection
        except Error as e:
            logger.error(f"从连接池获取连接失败: {e}")
            return None

    def generate_random_time(self):
        """生成2025年全年的随机时间"""
        # 2025年的开始和结束时间
        start_date = datetime(self.target_year, 1, 1)
        end_date = datetime(self.target_year, 12, 31, 23, 59, 59)

        # 计算总的时间范围（秒数）
        total_seconds = int((end_date - start_date).total_seconds())

        # 生成随机秒数
        random_seconds = random.randint(0, total_seconds)

        # 计算随机时间
        random_time = start_date + timedelta(seconds=random_seconds)
        return random_time

    def generate_order_id(self, base_time):
        """生成订单ID - 使用时间戳和UUID确保唯一性"""
        date_prefix = base_time.strftime('%y%m%d')
        # 使用时间戳微秒和随机数确保唯一性
        timestamp_micro = str(int(base_time.timestamp() * 1000000))[-8:]  # 取后8位微秒
        # 添加线程ID和随机数增加唯一性
        thread_id = threading.current_thread().ident % 1000  # 取线程ID的后3位
        random_suffix = ''.join(random.choices(string.ascii_lowercase + string.digits, k=3))
        return f"{date_prefix}{timestamp_micro}{thread_id:03d}{random_suffix}"

    def generate_card_code(self):
        """生成15位卡号 - 使用UUID确保唯一性"""
        # 使用UUID生成唯一标识，然后转换为15位字符串
        unique_id = str(uuid.uuid4()).replace('-', '')[:15].upper()
        return unique_id

    def generate_extra_id(self, card_code):
        """生成extra_id - 基于卡号确保唯一性"""
        return f"C{card_code[:11]}"

    def check_order_exists(self, connection, order_id):
        """检查订单是否已存在"""
        cursor = None
        try:
            cursor = connection.cursor()
            cursor.execute("SELECT COUNT(*) FROM orders WHERE id = %s", (order_id,))
            count = cursor.fetchone()[0]
            return count > 0
        except Exception as e:
            logger.warning(f"检查订单存在性时出错: {e}")
            return False
        finally:
            if cursor:
                cursor.close()

    def generate_business_data(self, connection=None, max_retries=5):
        """生成一套完整的业务数据"""
        for attempt in range(max_retries):
            # 基础随机数据
            card_type = random.choice(self.card_types)
            face_value = random.choice(self.face_values)
            exchange_rate = round(random.uniform(5.0000, 7.0000), 4)

            # 计算金额
            ck_total_amount = round(face_value * exchange_rate, 2)  # 出卡总额
            sk_total_amount = round(ck_total_amount * 0.9, 2)      # 收卡总额
            commission = round(ck_total_amount * 0.1, 2)          # 手续费

            # 随机选择出卡类型：50% APP出卡，50% PC端出卡商出卡
            is_app_order = random.choice([True, False])

            if is_app_order:
                # APP出卡逻辑
                ck_owner_type = 'User'
                ck_owner_id = random.randint(25000, 27000)  # APP用户ID范围
                ck_company_id = None  # APP出卡时company_id为null
            else:
                # PC端出卡商出卡逻辑
                available_ck_companies = [cid for cid in self.ck_company_ids if cid in self.ck_company_owners]
                ck_company_id = random.choice(available_ck_companies)
                ck_owner_type = 'AdminUser'
                ck_owner_id = random.choice(self.ck_company_owners[ck_company_id])

            # 收卡商逻辑保持不变
            available_sk_companies = [cid for cid in self.sk_company_ids if cid in self.sk_company_owners]
            sk_company_id = random.choice(available_sk_companies)
            sk_owner_id = random.choice(self.sk_company_owners[sk_company_id])

            # 生成时间
            base_time = self.generate_random_time()

            # 生成ID
            order_id = self.generate_order_id(base_time)
            card_code = self.generate_card_code()

            # 如果提供了连接，检查订单是否已存在
            if connection and self.check_order_exists(connection, order_id):
                logger.debug(f"订单ID {order_id} 已存在，重新生成 (尝试 {attempt + 1}/{max_retries})")
                time.sleep(0.001)  # 短暂延迟确保时间戳不同
                continue

            return {
                'card_type': card_type,
                'face_value': face_value,
                'exchange_rate': exchange_rate,
                'ck_total_amount': ck_total_amount,
                'sk_total_amount': sk_total_amount,
                'commission': commission,
                'ck_company_id': ck_company_id,
                'sk_company_id': sk_company_id,
                'ck_owner_type': ck_owner_type,
                'ck_owner_id': ck_owner_id,
                'sk_owner_id': sk_owner_id,
                'base_time': base_time,
                'order_id': order_id,
                'card_code': card_code,
                'is_app_order': is_app_order
            }

        # 如果多次尝试都失败，返回最后一次生成的数据
        logger.warning(f"经过 {max_retries} 次尝试仍无法生成唯一订单ID，使用最后生成的数据")
        return {
            'card_type': card_type,
            'face_value': face_value,
            'exchange_rate': exchange_rate,
            'ck_total_amount': ck_total_amount,
            'sk_total_amount': sk_total_amount,
            'commission': commission,
            'ck_company_id': ck_company_id,
            'sk_company_id': sk_company_id,
            'ck_owner_type': ck_owner_type,
            'ck_owner_id': ck_owner_id,
            'sk_owner_id': sk_owner_id,
            'base_time': base_time,
            'order_id': order_id,
            'card_code': card_code,
            'is_app_order': is_app_order
        }

    def insert_single_business(self, connection, data):
        """插入一套完整的业务数据"""
        cursor = None
        try:
            cursor = connection.cursor()
            # 确保连接处于自动提交模式，然后开始事务
            connection.autocommit = False
            
            # 1. 插入orders表
            orders_sql = """
            INSERT INTO orders (
                id, company_id, owner_type, owner_id, status, withdrawal_status,
                beneficial_company_id, machine, operator_id, amount_profit_rmb,
                amount_performance_rmb, commission, amount_total_income_rmb,
                amount_total_withdraw_rmb, is_restock, is_image_link, user_source,
                created_at, updated_at, deleted_at, completed_order_cards_balance,
                rmb_income, amount_profit_rate, agent_operator_id
            ) VALUES (
                %s, %s, %s, %s, 2, %s, NULL, %s, %s, %s, %s, 0.00, %s, 0.00,
                0, 0, 0, %s, %s, NULL, %s, %s, 90.00, %s
            )
            """

            # 根据出卡类型设置不同的字段值
            if data['is_app_order']:
                # APP出卡：company_id为null，owner_type为User，withdrawal_status为0
                orders_values = (
                    data['order_id'], None, data['ck_owner_type'], data['ck_owner_id'],
                    0, None, None, 0.00, data['ck_total_amount'], 0.00,
                    data['base_time'].strftime('%Y-%m-%d %H:%M:%S'),
                    (data['base_time'] + timedelta(seconds=25)).strftime('%Y-%m-%d %H:%M:%S'),
                    data['face_value'], data['sk_total_amount'], None
                )
            else:
                # PC端出卡商出卡：正常的company_id和AdminUser，withdrawal_status为1
                orders_values = (
                    data['order_id'], data['ck_company_id'], data['ck_owner_type'], data['ck_owner_id'],
                    1, data['ck_company_id'], data['ck_owner_id'], data['sk_total_amount'],
                    data['ck_total_amount'], data['sk_total_amount'],
                    data['base_time'].strftime('%Y-%m-%d %H:%M:%S'),
                    (data['base_time'] + timedelta(seconds=28)).strftime('%Y-%m-%d %H:%M:%S'),
                    data['face_value'], data['ck_total_amount'], data['ck_owner_id']
                )
            
            cursor.execute(orders_sql, orders_values)
            
            # 2. 插入order_cards表
            order_cards_sql = """
            INSERT INTO order_cards (
                extra_id, card_type, balance, rate_balance, rate, rmb_rate,
                card_head_id, signature, card_info, exist_code, commission,
                buyer_commission, final_rmb_rate, final_rate, status, is_auto_compete,
                order_id, card_id, country_id, legacy_card_id, legacy_card_info,
                is_fast, chosen_buyer_company_ids, company_card_rates, machine,
                operator_id, batch, parent_order_card_id, created_at, updated_at,
                deleted_at, buyer_company_id, checked_at, local_checked_at,
                completed_at, local_completed_at, buyer_id, fail_reason_type,
                fail_reason, fail_reason_en, other_operator_id, correct_data,
                processed_at, balance_checked_at, extend_at, change_at, resend_at,
                vip_bonus, special_bonus, is_ocr_resolved, is_app_artificial_card,
                profit_rate, buyer_rmb_rate, is_auto_buy_failed, reward_amount,
                fission_divide_rmb_amount, is_app_order, user_confirm,
                user_confirmed_rate, is_rate_down, fission_commission_admin_user_rmb_amount,
                fission_commission_company_rmb_amount, reason_for_rate_down,
                rate_down_at, third_party_id, auto_balance_query_at, user_country_id
            ) VALUES (
                %s, 2, %s, %s, %s, %s, NULL, NULL, %s, NULL, %s, 0.00, %s, %s, 4, 0,
                %s, %s, 1, NULL, NULL, 1, NULL, NULL, %s, %s, 0, NULL, %s, %s, NULL,
                %s, %s, %s, %s, %s, %s, NULL, NULL, NULL, NULL, NULL, %s, NULL, NULL,
                NULL, NULL, NULL, NULL, 0, 0, 0.000, 0.0000, 0, 0.00, 0.00, 0, 0,
                0.0000, 0, 0.00, 0.00, NULL, NULL, NULL, NULL, 1
            )
            """
            
            card_info = f'{{"code":"{data["card_code"]}"}}'
            rate_value = data['face_value'] * 25  # 假设汇率换算
            
            extra_id = self.generate_extra_id(data['card_code'])
            order_cards_values = (
                extra_id, data['face_value'], data['face_value'],
                rate_value, data['exchange_rate'], card_info, data['commission'],
                data['exchange_rate'], rate_value, data['order_id'], data['card_type'],
                data['ck_company_id'], data['ck_owner_id'],
                data['base_time'].strftime('%Y-%m-%d %H:%M:%S'),
                (data['base_time'] + timedelta(seconds=12)).strftime('%Y-%m-%d %H:%M:%S'),
                data['sk_company_id'],
                (data['base_time'] + timedelta(seconds=10)).strftime('%Y-%m-%d %H:%M:%S'),
                (data['base_time'] + timedelta(seconds=10)).strftime('%Y-%m-%d %H:%M:%S'),
                (data['base_time'] + timedelta(seconds=12)).strftime('%Y-%m-%d %H:%M:%S'),
                (data['base_time'] + timedelta(seconds=12)).strftime('%Y-%m-%d %H:%M:%S'),
                data['sk_owner_id'],
                (data['base_time'] + timedelta(seconds=12)).strftime('%Y-%m-%d %H:%M:%S')
            )
            
            cursor.execute(order_cards_sql, order_cards_values)
            order_card_id = cursor.lastrowid
            
            # 3. 插入order_card_queues表
            queues_sql = """
            INSERT INTO order_card_queues (
                order_card_id, company_id, admin_user_id, card_rate, rmb_card_rate,
                expired_at, checked_at, estimate_check_at, delayed_times, info,
                status, buyer_mark, created_at, updated_at, deleted_at, reason,
                auto_buy_provider, auto_buy_provider_transaction_message,
                auto_buy_provider_order_id, auto_buy_provider_status, card_id, country_id
            ) VALUES (
                %s, %s, %s, %s, %s, %s, %s, NULL, 0, NULL, 2, NULL, %s, %s, NULL,
                NULL, 0, NULL, NULL, 0, %s, 1
            )
            """
            
            queues_values = (
                order_card_id, data['sk_company_id'], data['sk_owner_id'],
                rate_value, data['exchange_rate'],
                (data['base_time'] + timedelta(seconds=70)).strftime('%Y-%m-%d %H:%M:%S'),
                (data['base_time'] + timedelta(seconds=10)).strftime('%Y-%m-%d %H:%M:%S'),
                (data['base_time'] + timedelta(seconds=1)).strftime('%Y-%m-%d %H:%M:%S'),
                (data['base_time'] + timedelta(seconds=12)).strftime('%Y-%m-%d %H:%M:%S'),
                data['card_type']
            )
            
            cursor.execute(queues_sql, queues_values)
            
            # 4. 插入transactions表 (第一条 - 出卡记录)
            trans_sql_1 = """
            INSERT INTO transactions (
                company_id, rmb_amount, nr_amount, amount_currency, transaction_fee,
                exchange_rate, status, type, order_card_id, transactionable_type,
                transactionable_id, created_at, updated_at, deleted_at, owner_type,
                owner_id, withdrawal_id, recharge_id, phone_number_id
            ) VALUES (
                %s, %s, %s, 1, %s, '250', 1, 1, %s, NULL, NULL, %s, %s, NULL,
                %s, %s, NULL, NULL, NULL
            )
            """

            # 根据出卡类型设置不同的字段值
            if data['is_app_order']:
                # APP出卡：company_id为null，owner_type为User
                trans_values_1 = (
                    None, data['sk_total_amount'],
                    data['sk_total_amount'] * 250, data['commission'], order_card_id,
                    data['base_time'].strftime('%Y-%m-%d %H:%M:%S'),
                    (data['base_time'] + timedelta(seconds=12)).strftime('%Y-%m-%d %H:%M:%S'),
                    data['ck_owner_type'], data['ck_owner_id']
                )
            else:
                # PC端出卡商出卡：正常的company_id和AdminUser
                trans_values_1 = (
                    data['ck_company_id'], data['sk_total_amount'],
                    data['sk_total_amount'] * 250, data['commission'], order_card_id,
                    data['base_time'].strftime('%Y-%m-%d %H:%M:%S'),
                    (data['base_time'] + timedelta(seconds=12)).strftime('%Y-%m-%d %H:%M:%S'),
                    data['ck_owner_type'], data['ck_owner_id']
                )
            
            cursor.execute(trans_sql_1, trans_values_1)
            trans_id_1 = cursor.lastrowid
            
            # 5. 插入transactions表 (第二条 - 收卡记录)
            trans_sql_2 = """
            INSERT INTO transactions (
                company_id, rmb_amount, nr_amount, amount_currency, transaction_fee,
                exchange_rate, status, type, order_card_id, transactionable_type,
                transactionable_id, created_at, updated_at, deleted_at, owner_type,
                owner_id, withdrawal_id, recharge_id, phone_number_id
            ) VALUES (
                %s, %s, %s, 1, 0.00, '250', 1, 3, %s, NULL, NULL, %s, %s, NULL,
                'AdminUser', %s, NULL, NULL, NULL
            )
            """
            
            trans_values_2 = (
                data['sk_company_id'], data['ck_total_amount'],
                data['ck_total_amount'] * 250, order_card_id,
                (data['base_time'] + timedelta(seconds=10)).strftime('%Y-%m-%d %H:%M:%S'),
                (data['base_time'] + timedelta(seconds=12)).strftime('%Y-%m-%d %H:%M:%S'),
                data['sk_owner_id']
            )
            
            cursor.execute(trans_sql_2, trans_values_2)
            trans_id_2 = cursor.lastrowid
            
            # 6. 插入platform_transactions表
            platform_sql = """
            INSERT INTO platform_transactions (
                transaction_id, company_id, owner_type, owner_id, balance,
                is_settlement, after_balance, created_at, updated_at, type,
                transactionable_id, transactionable_type, remark, refund_id, status
            ) VALUES (
                %s, %s, 'AdminUser', %s, %s, 0, NULL, %s, %s, 3, %s,
                'App\\\\Models\\\\Transaction', NULL, NULL, 1
            )
            """
            
            platform_values = (
                trans_id_2, data['ck_company_id'], data['ck_owner_id'], data['commission'],
                (data['base_time'] + timedelta(seconds=12)).strftime('%Y-%m-%d %H:%M:%S'),
                (data['base_time'] + timedelta(seconds=12)).strftime('%Y-%m-%d %H:%M:%S'),
                trans_id_2
            )
            
            cursor.execute(platform_sql, platform_values)

            # 提交事务
            connection.commit()
            return True
            
        except Error as e:
            # 回滚事务
            try:
                connection.rollback()
            except:
                pass
            error_code = getattr(e, 'errno', 'unknown')
            error_msg = getattr(e, 'msg', str(e))
            sqlstate = getattr(e, 'sqlstate', 'unknown')
            logger.error(f"MySQL错误 {error_code} (SQLSTATE: {sqlstate}): {error_msg}")
            logger.error(f"订单ID: {data.get('order_id', 'unknown')}, 卡号: {data.get('card_code', 'unknown')}")
            return False
        except Exception as e:
            # 回滚事务
            try:
                connection.rollback()
            except:
                pass
            logger.error(f"插入数据时发生未知错误: {type(e).__name__}: {e}, 订单ID: {data.get('order_id', 'unknown')}")
            return False
        finally:
            if cursor:
                cursor.close()

    def insert_with_retry(self, connection, data, max_retries=3):
        """带重试机制的插入函数"""
        for attempt in range(max_retries):
            try:
                if self.insert_single_business(connection, data):
                    return True
                else:
                    if attempt < max_retries - 1:
                        time.sleep(0.1 * (attempt + 1))  # 递增延迟
            except Exception as e:
                logger.warning(f"插入重试 {attempt + 1}/{max_retries} 失败: {e}")
                if attempt < max_retries - 1:
                    time.sleep(0.1 * (attempt + 1))
                else:
                    logger.error(f"插入最终失败，已重试 {max_retries} 次")
        return False

    def worker_thread(self, work_queue, results_queue, thread_id):
        """工作线程函数"""
        connection = self.get_connection()
        if not connection:
            logger.error(f"线程 {thread_id}: 数据库连接失败")
            results_queue.put((thread_id, 0, 0))
            return
        
        success_count = 0
        error_count = 0
        
        try:
            while True:
                try:
                    # 从队列获取任务，如果队列为空等待1秒后退出
                    work_item = work_queue.get(timeout=1)
                except queue.Empty:
                    break
                
                try:
                    data = self.generate_business_data(connection)
                    if self.insert_with_retry(connection, data):
                        success_count += 1
                    else:
                        error_count += 1
                        
                    work_queue.task_done()
                    
                    # 每处理50条记录输出一次进度
                    if (success_count + error_count) % 50 == 0:
                        logger.info(f"线程 {thread_id}: 已处理 {success_count + error_count} 条记录 "
                                  f"(成功: {success_count}, 失败: {error_count})")
                              
                except Exception as e:
                    if isinstance(e, Error):
                        error_code = getattr(e, 'errno', 'unknown')
                        error_msg = getattr(e, 'msg', str(e))
                        sqlstate = getattr(e, 'sqlstate', 'unknown')
                        logger.error(f"线程 {thread_id}: MySQL错误 {error_code} (SQLSTATE: {sqlstate}): {error_msg}")
                    else:
                        logger.error(f"线程 {thread_id}: 处理出错 {type(e).__name__}: {e}")
                    error_count += 1
                    work_queue.task_done()
        
        finally:
            if connection:
                connection.close()
            results_queue.put((thread_id, success_count, error_count))
            logger.info(f"线程 {thread_id}: 完成，成功: {success_count}, 失败: {error_count}")

    def generate_data(self):
        """主函数：生成数据"""
        # 如果连接池未初始化，现在初始化
        if self.connection_pool is None:
            self.init_connection_pool()

        logger.info(f"开始生成数据...")
        logger.info(f"目标年份: {self.target_year}年 (全年分布)")
        logger.info(f"生成数量: {self.total_records} 条")
        logger.info(f"线程数量: {self.thread_count}")
        
        # 根据数据量调整线程数
        actual_thread_count = min(self.thread_count, self.total_records)  # 移除硬编码的5线程限制
        if actual_thread_count != self.thread_count:
            logger.info(f"线程数调整为: {actual_thread_count}")
        
        # 创建任务队列
        work_queue = queue.Queue()
        results_queue = queue.Queue()
        
        # 向队列添加任务
        for i in range(self.total_records):
            work_queue.put(i)
        
        # 启动线程
        threads = []
        start_time = time.time()
        
        for i in range(actual_thread_count):
            t = threading.Thread(
                target=self.worker_thread,
                args=(work_queue, results_queue, i+1),
                name=f"Worker-{i+1}"
            )
            t.start()
            threads.append(t)
        
        # 等待所有线程完成
        for t in threads:
            t.join()
        
        # 收集结果
        total_success = 0
        total_error = 0
        
        while not results_queue.empty():
            thread_id, success, error = results_queue.get()
            total_success += success
            total_error += error
        
        end_time = time.time()
        duration = end_time - start_time
        
        logger.info(f"\n数据生成完成!")
        logger.info(f"总耗时: {duration:.2f} 秒")
        logger.info(f"成功生成: {total_success} 条业务数据")
        logger.info(f"失败数量: {total_error} 条")
        if duration > 0:
            logger.info(f"平均速度: {total_success/duration:.2f} 条/秒")

    def __del__(self):
        """析构函数，清理连接池"""
        if hasattr(self, 'connection_pool') and self.connection_pool:
            try:
                # 关闭连接池中的所有连接
                pass  # mysql.connector的连接池会自动管理连接
            except:
                pass

if __name__ == "__main__":
    generator = CardDataGenerator()
    
    # 可以在这里修改配置
    generator.target_year = 2025
    generator.total_records = 200000  # 修改生成数量
    generator.thread_count = 10  # 修改线程数
    
    try:
        generator.generate_data()
    except KeyboardInterrupt:
        logger.info("程序被用户中断")
    except Exception as e:
        logger.error(f"程序执行出错: {e}")
    finally:
        logger.info("程序结束")
